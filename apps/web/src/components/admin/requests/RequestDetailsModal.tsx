import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Chip,
  Card,
  CardContent,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  TrendingUp as IncreaseIcon,
  Schedule as ExtendIcon,
  Add as AddIcon,
  ExpandMore as QuantityIcon,
  Person as PersonIcon,
  Assessment as RiskIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import { AdminRequest } from '../../../services/admin-requests.service';

interface RequestDetailsModalProps {
  open: boolean;
  request: AdminRequest | null;
  onClose: () => void;
}

const RequestDetailsModal: React.FC<RequestDetailsModalProps> = ({
  open,
  request,
  onClose
}) => {
  if (!request) return null;

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('h:mm a, dd MMM yyyy');
  };

  const getRequestTypeIcon = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return <IncreaseIcon sx={{ color: '#ff9800' }} />;
      case 'extend_tp':
        return <ExtendIcon sx={{ color: '#2196f3' }} />;
      case 'add_22_thc':
        return <AddIcon sx={{ color: '#4caf50' }} />;
      case 'quantity_increase':
        return <QuantityIcon sx={{ color: '#9c27b0' }} />;
      default:
        return <RiskIcon sx={{ color: '#757575' }} />;
    }
  };

  const getRequestTypeLabel = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return 'THC Increase to 29%';
      case 'extend_tp':
        return 'Extend Treatment Plan';
      case 'add_22_thc':
        return 'Add 22% THC Option';
      case 'quantity_increase':
        return 'Quantity Increase Request';
      default:
        return 'Unknown Request Type';
    }
  };

  const getRiskScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 70) return '#4caf50'; // Green
    if (percentage >= 40) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getRiskLevel = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 70) return 'Low Risk';
    if (percentage >= 40) return 'Medium Risk';
    return 'High Risk';
  };

  // Parse questionnaire data if available
  const questionnaireData = request.questionnaire_data ?
    (typeof request.questionnaire_data === 'string' ?
      JSON.parse(request.questionnaire_data) :
      request.questionnaire_data) : null;

  // Handle different questionnaire data structures
  const responses = Array.isArray(questionnaireData)
    ? questionnaireData
    : (questionnaireData?.responses || []);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getRequestTypeIcon(request.type)}
          <Typography variant="h6">
            Request Details - {getRequestTypeLabel(request.type)}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {/* Patient Information */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PersonIcon />
              Patient Information
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Name</Typography>
                <Typography variant="body1" fontWeight="bold">{request.patient_name}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Patient ID</Typography>
                <Typography variant="body1">{request.patient_id}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Email</Typography>
                <Typography variant="body1">{request.email}</Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Request Details */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <RiskIcon />
              Request Details
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, mb: 2, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Request Type</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getRequestTypeIcon(request.type)}
                  <Typography variant="body1">{getRequestTypeLabel(request.type)}</Typography>
                </Box>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Risk Assessment</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={`${request.total_score}/${request.max_score}`}
                    size="small"
                    sx={{
                      backgroundColor: getRiskScoreColor(request.total_score, request.max_score),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    ({getRiskLevel(request.total_score, request.max_score)})
                  </Typography>
                </Box>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Status</Typography>
                <Chip
                  icon={request.status === 'approved' ? <ApproveIcon /> : 
                        request.status === 'rejected' ? <RejectIcon /> : undefined}
                  label={request.status.toUpperCase()}
                  size="small"
                  sx={{
                    backgroundColor: request.status === 'approved' ? '#008000' : 
                                   request.status === 'rejected' ? undefined : '#ff9800',
                    color: request.status === 'approved' ? 'white' : 
                           request.status === 'rejected' ? undefined : 'white',
                    '& .MuiChip-icon': {
                      color: request.status === 'approved' ? 'white' : undefined
                    }
                  }}
                  color={request.status === 'rejected' ? 'error' : undefined}
                />
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Quantity Increase Details */}
        {request.type === 'quantity_increase' && request.strength_requests && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                Quantity Increase Details
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {request.strength_requests.map((strengthRequest: any, index: number) => (
                  <Box
                    key={index}
                    sx={{
                      p: 2,
                      backgroundColor: '#f8f9fa',
                      borderRadius: 1,
                      border: '1px solid #e9ecef'
                    }}
                  >
                    <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {strengthRequest.strength}% THC Quantity Change:
                    </Typography>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 1,
                      backgroundColor: 'white',
                      borderRadius: 0.5,
                      border: '1px solid #dee2e6'
                    }}>
                      <Typography variant="body2">
                        <span style={{ color: '#6c757d' }}>Current: {strengthRequest.currentQuantity}g</span>
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#007F00', fontWeight: 'bold' }}>
                        → Requested: {strengthRequest.requestedQuantity}g
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#28a745', fontSize: '0.9em' }}>
                        (+{strengthRequest.increaseAmount}g)
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Questionnaire Responses */}
        {responses.length > 0 && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                Questionnaire Responses
              </Typography>

              {/* Questions Summary */}
              <Box sx={{ mb: 2, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Questions Summary
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label={`Total Questions: ${responses.length}`}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={`Scored Questions: ${responses.filter((r: any) => r.score > 0).length}`}
                    size="small"
                    sx={{
                      backgroundColor: '#4caf50',
                      color: 'white'
                    }}
                  />
                  <Chip
                    label={`Zero Score: ${responses.filter((r: any) => r.score === 0).length}`}
                    size="small"
                    sx={{
                      backgroundColor: '#f44336',
                      color: 'white'
                    }}
                  />
                </Box>
              </Box>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell><strong>Question</strong></TableCell>
                      <TableCell><strong>Answer</strong></TableCell>
                      <TableCell align="center"><strong>Score</strong></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {responses.map((response: any, index: number) => (
                      <TableRow key={index} hover>
                        <TableCell sx={{ maxWidth: 300 }}>
                          <Typography variant="body2">
                            {response.questionText || response.question || `Question ${index + 1}`}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ maxWidth: 200 }}>
                          <Typography variant="body2">
                            {response.answerText || response.answer || response.value || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={response.score || 0}
                            size="small"
                            variant="outlined"
                            sx={{
                              backgroundColor: response.score > 0 ? '#4caf50' : '#f44336',
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {/* Timeline */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              Timeline
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Submitted</Typography>
                <Typography variant="body1">{formatTimestamp(request.created_at)}</Typography>
              </Box>
              {request.reviewed_at && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Processed</Typography>
                  <Typography variant="body1">{formatTimestamp(request.reviewed_at)}</Typography>
                </Box>
              )}
              {request.doctor_name && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Processed By</Typography>
                  <Typography variant="body1">{request.doctor_name}</Typography>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Processing Details (if processed) */}
        {(request.status === 'approved' || request.status === 'rejected') && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                Processing Details
              </Typography>
              <Box sx={{ display: 'flex', gap: 4 }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" color="text.secondary">Decision</Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {request.status === 'approved' ? 'Approved' : 'Rejected'}
                  </Typography>
                </Box>
                {request.review_notes && (
                  <Box sx={{ flex: 2 }}>
                    <Typography variant="body2" color="text.secondary">Notes</Typography>
                    <Typography variant="body1">{request.review_notes}</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RequestDetailsModal;
