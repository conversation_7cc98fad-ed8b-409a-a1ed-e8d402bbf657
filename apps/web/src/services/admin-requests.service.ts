import axios from 'axios';
import { config } from '../config';

const API_BASE_URL = config.apiUrl || 'http://localhost:8080/api';

export interface AdminRequest {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  patient_id: string;
  email: string;
  patient_name: string;
  total_score: number;
  max_score: number;
  status: 'pending' | 'submitted' | 'approved' | 'rejected';
  created_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  review_notes?: string;
  doctor_name?: string;
  questionnaire_data?: any;
  strength_requests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
}

export interface AdminRequestStats {
  pending: number;
  approved: number;
  rejected: number;
  total: number;
}

class AdminRequestsService {
  /**
   * Get pending requests for admin view
   */
  async getPendingRequests(): Promise<{ requests: AdminRequest[] }> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/pending`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get processed requests for admin view
   */
  async getProcessedRequests(
    status?: 'approved' | 'rejected',
    type?: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase',
    limit = 50,
    page = 1
  ): Promise<{ requests: AdminRequest[] }> {
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (type) params.append('type', type);
    params.append('limit', limit.toString());
    params.append('page', page.toString());

    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/processed?${params}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get request statistics for admin dashboard
   */
  async getRequestStats(): Promise<AdminRequestStats> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/stats`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get patient request history for admin lookup
   */
  async getPatientRequestHistory(patientId: string, limit = 20): Promise<{ requests: AdminRequest[] }> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/patient/${patientId}?limit=${limit}`,
      { withCredentials: true }
    );
    return response.data.data;
  }
}

export const adminRequestsService = new AdminRequestsService();
export default adminRequestsService;
